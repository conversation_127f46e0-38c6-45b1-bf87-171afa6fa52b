package com.gianghp.dried_tea_shop.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Entity
@Table(name = "discount")
@Getter
@Setter
public class Discount extends BaseEntity {
    
    @Column(name = "name", nullable = false, length = 200)
    private String name;
    
    @Column(name = "discount_type", nullable = false, length = 10)
    private String discountType;
    
    @Column(name = "value", nullable = false, precision = 12, scale = 2)
    private BigDecimal value;
    
    @Column(name = "start_date", nullable = false)
    private LocalDate startDate;
    
    @Column(name = "end_date", nullable = false)
    private LocalDate endDate;
    
    @Column(name = "active", nullable = false)
    private Boolean active = true;
    
    // Relationships
    @OneToMany(mappedBy = "discount", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Product> products;
}
